"use client";

import { Breed } from "@/services/_type";
import { useState, useEffect } from "react";

interface BreedsAutoCompleteProps {
  breeds: Breed[];
  onSelect: (breed: Breed) => void;
}

const BreedsAutoComplete: React.FC<BreedsAutoCompleteProps> = ({
  breeds,
  onSelect,
}) => {
  const [query, setQuery] = useState<string>("");
  const [filteredBreeds, setFilteredBreeds] = useState<Breed[]>([]);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);

  useEffect(() => {
    if (query.length === 0) {
      setFilteredBreeds([]);
      setShowDropdown(false);
      return;
    }
    const matches = breeds.filter((breed) =>
      breed.name.toLowerCase().startsWith(query.toLowerCase())
    );
    setFilteredBreeds(matches);
    setShowDropdown(matches.length > 0);
  }, [query, breeds]);

  const handleSelect = (breed: Breed) => {
    setQuery(breed.name);
    setShowDropdown(false);
    onSelect(breed);
  };

  return (
    <div className="relative w-full sm:w-[250px]">
      <input
        type="text"
        placeholder="search heres .........."
        className="w-full border rounded-md px-4 py-2"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onFocus={() => query.length > 0 && setShowDropdown(true)}
      />
      {showDropdown && (
        <ul className="absolute top-full left-0 w-full bg-white border rounded-md shadow-md max-h-60 overflow-y-auto z-50">
          {filteredBreeds.map((breed) => (
            <li
              key={breed.id}
              className="px-4 py-2 hover:bg-gray-200 cursor-pointer"
              onClick={() => handleSelect(breed)}
            >
              {breed.name}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default BreedsAutoComplete;
