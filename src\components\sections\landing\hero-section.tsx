"use client";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  ContentContainer,
  DropDown,
  DynamicColorPolygon,
  <PERSON>ing,
  LottieAnimator,
  NextImagePlaceHolder,
  RingPulse,
  SectionContainer,
} from "@/components";
import { imageConfigs, polygonConfigs } from "@/constants/raw-objects";
import {
  LANDING_HERO_SECTION_SUB_TITLE,
  LANDING_HERO_SECTION_TITLE,
} from "@/constants/text-objects";
import clsx from "clsx";
import scrollJSON from "../../../animations/scroll-down.json";
import BreedsAutoComplete from "./breed-auto-complete";
import { useGetBreeds } from "@/hooks";
import { Breed } from "@/services/_type";

const LandingPageHeroSection = () => {
    const { breeds } = useGetBreeds(true);
  const [selectedBreed, setSelectedBreed] = useState<Breed | null>(null);
  return (
    <SectionContainer
      background="SkySlate"
      classNames="min-h-[calc(100svh_-_112px)] h-fit w-full flex justify-center items-center relative overflow-hidden"
    >
      {polygonConfigs.map((polygon, idx) => (
        <DynamicColorPolygon key={idx} classNames={polygon.classNames} />
      ))}
      <div className="hidden md:inline-block">
        {imageConfigs.map(({ top, left, src }, idx) => (
          <div
            key={idx}
            className={clsx("absolute p-2 rounded-full opacity-70", top, left)}
          >
            <div className="relative w-[120px] h-[120px] ">
              <RingPulse duration={2} />
              <div className="rounded-full  overflow-hidden">
                <NextImagePlaceHolder
                  src={src}
                  width={120}
                  height={120}
                  classNames="relative z-10 scale-120"
                />
              </div>
            </div>
          </div>
        ))}
      </div>
      <ContentContainer classNames="flex justify-center">
        <div className="w-full xl:w-4/5 h-fit z-30 flex flex-col gap-6">
          <div className="w-full flex flex-col sm:flex-row justify-center items-center gap-4">
            <NextImagePlaceHolder
              src="/logo.png"
              width={160}
              height={100}
              classNames="opacity-50"
            />
            <Heading
              type="Title"
              title={LANDING_HERO_SECTION_TITLE}
              lowContrast
            />
          </div>
          <div className="mb-4 flex justify-center">
            <Heading
              type="Hx1"
              title={LANDING_HERO_SECTION_SUB_TITLE}
              lowContrast
              classNames="text-center inline-block text-(--black)!"
            />
          </div>
          <div className="bg-white  min-h-16 h-fit rounded-4xl flex items-center justify-between border border-(--violet-light) flex-col md:flex-row gap-3 sm:gap-0 p-2 sm:p-4 px-4 sm:px-8">
            <div className="border sm:border-0 sm:border-r-[1px] border-[var(--full-gray)] pr-0 sm:pr-16 pl-10 sm:pl-0 w-full sm:w-[250px] rounded-3xl sm:rounded-none">
              <DropDown
                options={["Canine", "Feline"]}
                defaultValue="Feline"
                onChange={(val) => console.log("Selected:", val)}
              />
            </div>
            <div className="font-semibold sm:font-medium">
             <input placeholder="search Location  .........." />
            </div>
            <div className="border sm:border-0 sm:border-l-1 border-(--full-gray) px-2 py-2 pl-12 rounded-3xl sm:rounded-none  w-full sm:w-fit">
              <BreedsAutoComplete
              breeds={breeds || []}
              onSelect={(breed) => setSelectedBreed(breed)}
            />
            </div>
            <Button
              title="Search"
              type="type2Primary"
              classNames="w-full sm:w-fit"
            />
          </div>
          <div className="flex justify-center items-center">
            <div className="h-14 w-14">
              <LottieAnimator json={scrollJSON} />
            </div>
          </div>
        </div>
      </ContentContainer>
    </SectionContainer>
  );
};

export default LandingPageHeroSection;
